<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Signedtrue</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="An easy-to-use web application that lets you fill out, edit, and sign PDF documents anytime you need to!"
    />
    <meta property="og:title" content="Signedtrue" />
    <meta
      property="og:description"
      content="An easy-to-use web application that lets you fill out, edit, and sign PDF documents anytime you need to!"
    />
    <meta property="og:url" content="https://www.signedtrue.com/excuse-note/" />
    <link
      rel="preload"
      as="image"
      href="./signedtrue-hd-white-transparent.webp"
      type="image/webp"
    />

    <link
      rel="preload"
      as="image"
      href="./doctors-note-1.webp"
      type="image/webp"
    />
    <link href="./style.css" rel="stylesheet" />


    <script>
      function getCookie(name) {
        let cookieArr = document.cookie.split(";");
        for (let i = 0; i < cookieArr.length; i++) {
          let cookie = cookieArr[i].trim();
          if (cookie.startsWith(name + "=")) {
            return cookie.substring(name.length + 1); // Return the cookie value
          }
        }
        return null; // Return null if the cookie is not found
      }

      // Function to set a cookie with a specific name and value
      function setCookie(name, value, days) {
        let expires = "";
        if (days) {
          let date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // Set expiration date
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie =
          name + "=" + value + expires + "; path=/; domain=.signedtrue.com"; // Set the cookie
      }
      let flags = {
        flow: "02",
        ppg: "02",
      };

      Object.keys(flags).forEach((key) => {
        let cookieValue = getCookie(key);
        if (!cookieValue) {
          setCookie(key, flags[key], 7);
        }
      });
    </script>
  </head>
  <body>
    <div class="overlay">
      <div class="overlay-header">
        <img
          decoding="async"
          src="./signedtrue-hd-white-transparent.webp"
          alt="Logo"
          height="50px"
          width="100%"
        />
      </div>

      <div class="content-scroll-area">
        <img
          decoding="async"
          src="./doctors-note-1.webp"
          class="bg-image"
          alt="Background"
        />
        <div class="overlay-color"></div>

        <div class="overlay-content-wrapper">
          <div class="overlay-content">
            <p>Get Your</p>
            <h1>Excuse Note</h1>
            <p>in Minutes</p>
            <p class="sub-text">Quick, Reliable, and Hassle-free.</p>
            <a href="https://app.signedtrue.com/wizard/note/" class="button"
              >GET STARTED</a
            >
          </div>
        </div>
      </div>

      <div class="overlay-footer">
        <p class="copyright">Copyright © 2025 signedtrue.com</p>
        <p>
          <a href="https://www.signedtrue.com/privacy-policy/"
            >Privacy Policy</a
          >
          |
          <a href="https://www.signedtrue.com/terms-and-conditions/"
            >Terms of Use</a
          >
        </p>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        var cookieBanner = document.getElementById("cookie-law-info-bar");
        if (cookieBanner) {
          cookieBanner.remove();
        }
      });
    </script>
  </body>
</html>
